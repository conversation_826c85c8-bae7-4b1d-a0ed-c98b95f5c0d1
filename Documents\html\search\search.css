/*---------------- Search Box positioning */

#main-menu > li:last-child {
    /* This <li> object is the parent of the search bar */
    display: flex;
    justify-content: center;
    align-items: center;
    height: 43px;
    margin-right: 0;
}

/*---------------- Search box styling */

.SRPage * {
    font-weight: normal;
    line-height: normal;
}

dark-mode-toggle {
    margin-left: 5px;
    display: flex;
    float: right;
}

#MSearchBox {
    display: inline-block;
    white-space : nowrap;
    background: var(--search-background-color);
    border-radius: 0.65em;
    border: 1px solid var(--search-box-border-color);
    z-index: 102;
    margin-right: 4px;
}

#MSearchBox .left {
    display: inline-block;
    vertical-align: middle;
    height: 1.6em;
}

#MSearchField {
    display: inline-block;
    vertical-align: top;
    width: 7.5em;
    height: 22px;
    margin: 0 0 0 0.15em;
    padding: 0;
    line-height: 1em;
    border:none;
    color: var(--search-foreground-color);
    outline: none;
    font-family: var(--font-family-search);
    -webkit-border-radius: 0px;
    border-radius: 0px;
    background: none;
}

@media(hover: none) {
    /* to avoid zooming on iOS */
    #MSearchField {
        font-size: 16px;
    }
}

#MSearchBox .right {
    display: inline-block;
    vertical-align: middle;
    width: 1.4em;
    height: 1.6em;
}

#MSearchClose {
    display: none;
    font-size: inherit;
    background : none;
    border: none;
    margin: 0;
    padding: 0;
    outline: none;

}

#MSearchCloseImg {
    margin: 6px 0 0 4px;
}

.close-icon {
  width: 11px;
  height: 11px;
  background-color: var(--search-close-icon-bg-color);
  border-radius: 50%;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: content-box;
}

.close-icon:before,
.close-icon:after {
  content: '';
  position: absolute;
  width: 7px;
  height: 1px;
  background-color: var(--search-close-icon-fg-color);
}

.close-icon:before {
  transform: rotate(45deg);
}

.close-icon:after {
  transform: rotate(-45deg);
}


.MSearchBoxActive #MSearchField {
    color: var(--search-active-color);
}

.search-icon {
    width: 20px;
    height: 20px;
    display: inline-block;
    position: relative;
    margin-left: 3px;
}

#MSearchSelectExt.search-icon {
    width: 10px;
}

#MSearchSelectExt + input {
   margin-left: 5px;
}

.search-icon::before, .search-icon::after {
    content: '';
    position: absolute;
    border: 1.5px solid var(--search-foreground-color);
    box-sizing: content-box;
}

.search-icon::before {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    top: 7px;
    left: 2px;
    background: var(--search-background-color);
}

.search-icon::after {
    border: 1px solid var(--search-foreground-color);
    width: 0px;
    height: 3px;
    border-radius: 2px;
    top: 15px;
    left: 8px;
    transform: rotate(-45deg);
    transform-origin: top left;
}

.search-icon-dropdown {
    content: '';
    width: 0;
    height: 0;
    border-left: 3px solid transparent;
    border-right: 3px solid transparent;
    border-top: 3px solid var(--search-foreground-color);
    top: 8px;
    left: 15px;
    transform: translateX(-50%);
    position: absolute;
}




/*---------------- Search filter selection */

#MSearchSelectWindow {
    display: none;
    position: absolute;
    left: 0; top: 0;
    border: 1px solid var(--search-filter-border-color);
    background-color: var(--search-filter-background-color);
    backdrop-filter: var(--search-filter-backdrop-filter);
    -webkit-backdrop-filter: var(--search-filter-backdrop-filter);
    z-index: 10001;
    padding-top: 4px;
    padding-bottom: 4px;
    border-radius: 4px;
}

.SelectItem {
    font: 8pt var(--font-family-search);
    padding-left:  2px;
    padding-right: 12px;
    border: 0px;
}

span.SelectionMark {
    margin-right: 4px;
    font-family: var(--font-family-monospace);
    outline-style: none;
    text-decoration: none;
}

a.SelectItem {
    display: block;
    outline-style: none;
    color: var(--search-filter-foreground-color);
    text-decoration: none;
    padding-left:   6px;
    padding-right: 12px;
}

a.SelectItem:focus,
a.SelectItem:active {
    color: var(--search-filter-foreground-color);
    outline-style: none;
    text-decoration: none;
}

a.SelectItem:hover {
    color: var(--search-filter-highlight-text-color);
    background-color: var(--search-filter-highlight-bg-color);
    outline-style: none;
    text-decoration: none;
    cursor: pointer;
    display: block;
}

/*---------------- Search results window */

iframe#MSearchResults {
    /*width: 60ex;*/
    height: 15em;
}

@keyframes slideInSearchResults {
    from {
        opacity: 0;
        transform: translate(0, 15px);
    }

    to {
        opacity: 1;
        transform: translate(0, 20px);
    }
}

#MSearchResultsWindow {
    display: none;
    position: absolute;
    left: auto;
    right: 4px;
    top: 0;
    border: 1px solid var(--search-results-border-color);
    background-color: var(--search-results-background-color);
    backdrop-filter: var(--search-results-backdrop-filter);
    -webkit-backdrop-filter: var(--search-results-backdrop-filter);
    z-index:10000;
    width: 300px;
    height: 400px;
    overflow: auto;
    border-radius: 8px;
    transform: translate(0, 20px);
    animation: ease-out 280ms slideInSearchResults;
    box-shadow: 0 2px 8px 0 rgba(0,0,0,.075);
}


/* ----------------------------------- */


#SRIndex {
    clear:both; 
}

.SREntry {
    font-size: 10pt;
    padding-left: 1ex;
}

.SRPage .SREntry {
    font-size: 10pt;
    padding: 2px 5px;
}

div.SRPage {
    margin: 5px 2px;
    /*background-color: var(--search-results-background-color);*/
}

.SRChildren {
    padding-left: 3ex; padding-bottom: .5em 
}

.SRPage .SRChildren {
    display: none;
}

.SRSymbol {
    font-weight: bold;
    color: var(--search-results-foreground-color);
    font-family: var(--font-family-search);
    text-decoration: none;
    outline: none;
}

a.SRScope {
    display: block;
    color: var(--search-results-foreground-color);
    font-family: var(--font-family-search);
    font-size: 8pt;
    text-decoration: none;
    outline: none;
}

a.SRSymbol:focus, a.SRSymbol:active,
a.SRScope:focus, a.SRScope:active {
    text-decoration: underline;
}

span.SRScope {
    padding-left: 4px;
    font-family: var(--font-family-search);
}

.SRPage .SRStatus {
    padding: 2px 5px;
    font-size: 8pt;
    font-style: italic;
    font-family: var(--font-family-search);
}

.SRResult {
    display: none;
}

div.searchresults {
    margin-left: 10px;
    margin-right: 10px;
}

#searchBoxPos1 dark-mode-toggle {
    margin-top: 4px;
}

/*---------------- External search page results */

.pages b {
   color: var(--nav-foreground-color);
   padding: 5px 5px 3px 5px;
   background-color: var(--nav-menu-active-bg);
   border-radius: 4px;
}

.pages {
    line-height: 17px;
    margin-left: 4px;
    text-decoration: none;
}

.hl {
    font-weight: bold;
}

#searchresults {
    margin-bottom: 20px;
}

.searchpages {
    margin-top: 10px;
}

